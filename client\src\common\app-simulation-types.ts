export interface AppSimulationInputField {
  id: string;
  label: string;
  expectedValue: string;
  // Optional coordinates for clickable input fields (for mode 2)
  x1?: number;
  y1?: number;
  x2?: number;
  y2?: number;
  // Flag to indicate if this input is for mode 2 (clickable input)
  clickable?: boolean;
}

interface AppSimulationModalInputField {
  id: string;
  type: 'text' | 'textarea' | 'multiSelect' | 'range' | 'radio' | 'checkbox';
  label: string;
  dataId: string;
  required?: boolean;
  // For text/textarea
  placeholder?: string;
  defaultValue?: string | number | [number, number] | string[];
  // For multiSelect
  options?: string[];
  minSelections?: number;
  maxSelections?: number;
  // For range
  min?: number;
  max?: number;
  step?: number;
  labels?: { min: string; max: string };
  formatValue?: (value: any) => string;
  // For radio/checkbox
  radioOptions?: Array<{ value: string; label: string }>;
}

export interface AppSimulationModalConfig {
  title: string;
  description?: string;
  inputs: AppSimulationModalInputField[];
}

export interface AppSimulationScreen {
  id: string;
  title: string;
  image: string;
  bgColor: string;
  triggerMessage?: string;
  elements?: Array<{
    title: string;
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    left: number;
    top: number;
    width: number;
    height: number;
    backgroundColor?: string;
    border?: string;
    action: {
      type: string;
      dataContext?: string;
      dataContextId?: string;
      screenId?: string;
      message?: string;
      withData?: boolean;
      inputType?: 'input' | 'textarea';
      saveToSelections?: boolean;
      dropdownOptions?: Array<{
        label: string;
        screenId: string;
        dataContext?: string;
        dataContextId?: string;
        saveToSelections?: boolean;
      }>;
      modalConfig?: AppSimulationModalConfig;
    };
  }>;
  placeholders?: Array<{
    id: string;
    type: 'image' | 'text';
    dataId?: string;
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    title: string;
    initialValue?: string;
    style?: React.CSSProperties;
    dataByTime?: Array<{
      time: number;
      value: string;
    }>;
    increaseByTime?: boolean;
    increaseFrom?: number;
  }>;
  charts?: Array<{
    id: string;
    type: 'bar' | 'line';
    data?: any;
  }>;
  actions?: Array<{
    type: string;
    message?: string;
    withData?: boolean;
  }>;
}
